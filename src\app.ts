import express from 'express';
import path from 'path';
import session from 'express-session';
import flash from 'connect-flash';
import expressLayouts from 'express-ejs-layouts';
import cookieParser from 'cookie-parser';
import { config } from './config/env';
import {
  setupSecurity,
  errorHandler,
  notFound,
  authRateLimit,
  apiRateLimit,
} from './middlewares';
import routes from './routes';
import frontendRoutes from './routes/frontend';

const app = express();

// Trust proxy (important for rate limiting and IP detection)
app.set('trust proxy', 1);

// View engine setup
app.use(expressLayouts);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../views'));
app.set('layout', 'layouts/main');

// Static files
app.use(express.static(path.join(__dirname, '../public')));

// Session configuration
app.use(
  session({
    secret: config.JWT_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: config.NODE_ENV === 'production',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  })
);

// Flash messages
app.use(flash());

// Global template variables
app.use((req: any, res, next) => {
  res.locals.messages = req.flash ? req.flash() : {};
  next();
});

// Security middleware
app.use(setupSecurity());

// Cookie parsing middleware
app.use(cookieParser());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.NODE_ENV,
  });
});

// API routes with rate limiting for auth endpoints
app.use('/api/auth', authRateLimit);
// Apply more lenient rate limiting to general API routes
app.use('/api', apiRateLimit);
app.use('/api', routes);

// Frontend routes
app.use('/', frontendRoutes);

// 404 handler
app.use(notFound);

// Global error handler
app.use(errorHandler);

export default app;
