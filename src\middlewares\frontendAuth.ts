import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService';
import { User } from '../models';

export interface AuthenticatedFrontendRequest extends Request {
  user?: any;
  isAuthenticated?: boolean;
}

/**
 * Middleware to check authentication for frontend routes
 * Checks for JWT token in cookies and sets user data if valid
 */
export const checkFrontendAuth = async (
  req: AuthenticatedFrontendRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check for access token in cookies
    const accessToken = req.cookies?.accessToken;
    
    if (!accessToken) {
      req.isAuthenticated = false;
      req.user = null;
      next();
      return;
    }

    try {
      // Verify the access token
      const payload = AuthService.verifyAccessToken(accessToken);
      
      // Get user from database
      const user = await User.findById(payload.userId);
      if (!user) {
        // Clear invalid cookies
        res.clearCookie('accessToken');
        res.clearCookie('refreshToken');
        req.isAuthenticated = false;
        req.user = null;
        next();
        return;
      }

      // Check if device is still valid
      const device = user.devices.find(d => d.deviceId === payload.deviceId);
      if (!device) {
        // Clear invalid cookies
        res.clearCookie('accessToken');
        res.clearCookie('refreshToken');
        req.isAuthenticated = false;
        req.user = null;
        next();
        return;
      }

      // Set user data
      req.isAuthenticated = true;
      req.user = {
        id: user._id,
        email: user.email,
        fullName: user.fullName,
        deviceId: payload.deviceId
      };
      
      next();
    } catch (tokenError) {
      // Token is invalid or expired, try to refresh
      const refreshToken = req.cookies?.refreshToken;
      
      if (!refreshToken) {
        res.clearCookie('accessToken');
        req.isAuthenticated = false;
        req.user = null;
        next();
        return;
      }

      try {
        // Try to refresh the token
        const newTokens = await AuthService.refreshTokens(refreshToken);
        
        // Set new cookies
        res.cookie('accessToken', newTokens.accessToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 15 * 60 * 1000 // 15 minutes
        });
        
        res.cookie('refreshToken', newTokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
        });

        // Verify the new access token and get user
        const newPayload = AuthService.verifyAccessToken(newTokens.accessToken);
        const user = await User.findById(newPayload.userId);
        
        if (user) {
          req.isAuthenticated = true;
          req.user = {
            id: user._id,
            email: user.email,
            fullName: user.fullName,
            deviceId: newPayload.deviceId
          };
        } else {
          req.isAuthenticated = false;
          req.user = null;
        }
        
        next();
      } catch (refreshError) {
        // Refresh failed, clear cookies
        res.clearCookie('accessToken');
        res.clearCookie('refreshToken');
        req.isAuthenticated = false;
        req.user = null;
        next();
      }
    }
  } catch (error) {
    console.error('Frontend auth middleware error:', error);
    req.isAuthenticated = false;
    req.user = null;
    next();
  }
};

/**
 * Middleware to require authentication for frontend routes
 * Redirects to login if not authenticated
 */
export const requireFrontendAuth = (
  req: AuthenticatedFrontendRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.isAuthenticated) {
    res.redirect('/login');
    return;
  }

  next();
};

/**
 * Middleware to redirect authenticated users away from auth pages
 */
export const redirectIfAuthenticated = (
  req: AuthenticatedFrontendRequest,
  res: Response,
  next: NextFunction
): void => {
  if (req.isAuthenticated) {
    res.redirect('/dashboard');
    return;
  }

  next();
};
