import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

async function testFrontend() {
  try {
    console.log('🧪 Testing Frontend authentication flow...\n');

    // Test 1: Access dashboard without authentication (should redirect to login)
    console.log('1. Testing unauthenticated dashboard access...');
    const dashboardResponse = await fetch(`${BASE_URL}/dashboard`, {
      redirect: 'manual' // Don't follow redirects
    });
    
    console.log('Dashboard response status:', dashboardResponse.status);
    console.log('Dashboard response headers:', Object.fromEntries(dashboardResponse.headers.entries()));

    // Test 2: Access login page
    console.log('\n2. Testing login page access...');
    const loginPageResponse = await fetch(`${BASE_URL}/login`);
    console.log('Login page status:', loginPageResponse.status);
    
    if (loginPageResponse.ok) {
      console.log('✅ Login page accessible');
    } else {
      console.log('❌ Login page not accessible');
    }

    // Test 3: Test API login and then frontend access
    console.log('\n3. Testing API login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (loginResponse.ok) {
      console.log('✅ API login successful');
      
      // Extract cookies from login response
      const cookies = loginResponse.headers.get('set-cookie');
      console.log('Cookies received:', cookies);
      
      if (cookies) {
        // Test dashboard access with cookies
        console.log('\n4. Testing dashboard with session cookies...');
        const authenticatedDashboardResponse = await fetch(`${BASE_URL}/dashboard`, {
          headers: {
            'Cookie': cookies
          }
        });
        
        console.log('Authenticated dashboard status:', authenticatedDashboardResponse.status);
        
        if (authenticatedDashboardResponse.ok) {
          console.log('✅ Dashboard accessible with session');

          // Test other frontend routes
          console.log('\n5. Testing other frontend routes...');
          const routesToTest = [
            '/incomes',
            '/investments',
            '/donations',
            '/needs',
            '/wants',
            '/incomes/new',
            '/donations/new',
            '/profile'
          ];

          for (const route of routesToTest) {
            const routeResponse = await fetch(`${BASE_URL}${route}`, {
              headers: {
                'Cookie': cookies
              }
            });

            console.log(`${route}: ${routeResponse.status} ${routeResponse.ok ? '✅' : '❌'}`);
          }

        } else {
          console.log('❌ Dashboard not accessible with session');
        }
      }
    } else {
      console.log('❌ API login failed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFrontend();
