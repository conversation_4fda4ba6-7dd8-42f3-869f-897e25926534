import mongoose, { Document, Schema } from 'mongoose';

export interface IDonation extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number;
  description: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

const donationSchema = new Schema<IDonation>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Index for better query performance
donationSchema.index({ userId: 1, date: -1 });

export const Donation = mongoose.model<IDonation>('Donation', donationSchema);
