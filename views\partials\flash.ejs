<% if (typeof messages !== 'undefined') { %>
    <% if (messages.success && messages.success.length > 0) { %>
        <div class="container mt-3">
            <% messages.success.forEach(message => { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }) %>
        </div>
    <% } %>
    
    <% if (messages.error && messages.error.length > 0) { %>
        <div class="container mt-3">
            <% messages.error.forEach(message => { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }) %>
        </div>
    <% } %>
    
    <% if (messages.warning && messages.warning.length > 0) { %>
        <div class="container mt-3">
            <% messages.warning.forEach(message => { %>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }) %>
        </div>
    <% } %>
    
    <% if (messages.info && messages.info.length > 0) { %>
        <div class="container mt-3">
            <% messages.info.forEach(message => { %>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i><%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }) %>
        </div>
    <% } %>
<% } %>
