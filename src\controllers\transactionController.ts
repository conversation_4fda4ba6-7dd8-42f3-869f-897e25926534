import { Response } from 'express';
import { Model, Document } from 'mongoose';
import { AuthenticatedRequest } from '../middlewares/auth';

interface TransactionDocument extends Document {
  userId: any;
  amount: number;
  description: string;
  date: Date;
}

export class TransactionController {
  private model: Model<any>;
  private categoryName: string;

  constructor(model: Model<any>, categoryName: string) {
    this.model = model;
    this.categoryName = categoryName;
  }

  create = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { amount, description, date } = req.body;
      
      const transaction = new this.model({
        userId: req.user.id,
        amount,
        description,
        date: date ? new Date(date) : new Date(),
      });

      await transaction.save();

      res.status(201).json({
        message: `${this.categoryName} created successfully`,
        data: transaction,
      });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  getAll = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { page = 1, limit = 10, startDate, endDate } = req.query as any;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = { userId: req.user.id };
      
      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = new Date(startDate);
        if (endDate) query.date.$lte = new Date(endDate);
      }

      const [transactions, total] = await Promise.all([
        this.model
          .find(query)
          .sort({ date: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.model.countDocuments(query),
      ]);

      res.json({
        data: transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  getById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const transaction = await this.model.findOne({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!transaction) {
        res.status(404).json({ error: `${this.categoryName} not found` });
        return;
      }

      res.json({ data: transaction });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  update = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const updateData = { ...req.body };
      if (updateData.date) {
        updateData.date = new Date(updateData.date);
      }

      const transaction = await this.model.findOneAndUpdate(
        { _id: req.params.id, userId: req.user.id },
        updateData,
        { new: true, runValidators: true }
      );

      if (!transaction) {
        res.status(404).json({ error: `${this.categoryName} not found` });
        return;
      }

      res.json({
        message: `${this.categoryName} updated successfully`,
        data: transaction,
      });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  delete = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const transaction = await this.model.findOneAndDelete({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!transaction) {
        res.status(404).json({ error: `${this.categoryName} not found` });
        return;
      }

      res.json({ message: `${this.categoryName} deleted successfully` });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };
}
