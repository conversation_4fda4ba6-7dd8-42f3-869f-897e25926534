openapi: 3.0.0
info:
  title: Dhanfolio API
  description: Personal Finance Tracker API with budget allocation
  version: 1.0.0
  contact:
    name: <PERSON>hanfolio Support
    email: <EMAIL>

servers:
  - url: http://localhost:3000/api
    description: Development server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    cookieAuth:
      type: apiKey
      in: cookie
      name: accessToken

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          format: email
        fullName:
          type: string
        createdAt:
          type: string
          format: date-time

    Transaction:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        amount:
          type: number
          minimum: 0
        description:
          type: string
        date:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    DashboardData:
      type: object
      properties:
        totalIncome:
          type: number
        totalSpent:
          type: number
        totalRemaining:
          type: number
        overallBudgetStatus:
          type: string
          enum: [under, over, exact]
        budgetAllocation:
          type: object
          properties:
            needs:
              type: number
            wants:
              type: number
            investments:
              type: number
            donations:
              type: number

paths:
  /auth/register:
    post:
      summary: Register a new user
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password, fullName]
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
                fullName:
                  type: string
      responses:
        201:
          description: User registered successfully
        400:
          description: Validation error
        409:
          description: User already exists

  /auth/login:
    post:
      summary: Login user
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        200:
          description: Login successful
          headers:
            Set-Cookie:
              schema:
                type: string
                example: accessToken=abc123; HttpOnly; Secure
        401:
          description: Invalid credentials

  /dashboard:
    get:
      summary: Get dashboard data
      tags: [Dashboard]
      security:
        - bearerAuth: []
        - cookieAuth: []
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          schema:
            type: string
            format: date-time
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/DashboardData'

  /incomes:
    get:
      summary: Get user's income transactions
      tags: [Transactions]
      security:
        - bearerAuth: []
        - cookieAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
      responses:
        200:
          description: Income transactions retrieved successfully

    post:
      summary: Create new income transaction
      tags: [Transactions]
      security:
        - bearerAuth: []
        - cookieAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [amount, description]
              properties:
                amount:
                  type: number
                  minimum: 0
                description:
                  type: string
                date:
                  type: string
                  format: date-time
      responses:
        201:
          description: Income transaction created successfully

tags:
  - name: Authentication
    description: User authentication endpoints
  - name: Dashboard
    description: Dashboard and analytics endpoints
  - name: Transactions
    description: Transaction management endpoints
