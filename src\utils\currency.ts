/**
 * Currency formatting utilities for Indian Rupee (INR)
 */

/**
 * Format amount as Indian currency with proper formatting
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  if (isNaN(amount)) {
    return '₹0';
  }
  
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

/**
 * Format currency with Indian units (K, L, Cr) for large amounts
 * @param amount - The amount to format
 * @returns Formatted currency string with units
 */
export function formatCurrencyWithUnits(amount: number): string {
  if (isNaN(amount)) {
    return '₹0';
  }
  
  if (amount >= 10000000) { // 1 crore or more
    const crores = amount / 10000000;
    return `₹${crores.toFixed(1)} Cr`;
  } else if (amount >= 100000) { // 1 lakh or more
    const lakhs = amount / 100000;
    return `₹${lakhs.toFixed(1)} L`;
  } else if (amount >= 1000) { // 1 thousand or more
    const thousands = amount / 1000;
    return `₹${thousands.toFixed(1)} K`;
  } else {
    return `₹${amount.toLocaleString('en-IN')}`;
  }
}

/**
 * Format amount as plain number with Indian formatting (no currency symbol)
 * @param amount - The amount to format
 * @returns Formatted number string
 */
export function formatNumber(amount: number): string {
  if (isNaN(amount)) {
    return '0';
  }
  
  return new Intl.NumberFormat('en-IN').format(amount);
}

/**
 * Parse currency string back to number
 * @param currencyString - The currency string to parse
 * @returns Parsed number
 */
export function parseCurrency(currencyString: string): number {
  // Remove currency symbols and commas, then parse
  const cleanString = currencyString.replace(/[₹,\s]/g, '');
  return parseFloat(cleanString) || 0;
}
