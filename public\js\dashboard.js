// Dashboard functionality
let budgetChart = null;
let trendsChart = null;

// Prevent multiple simultaneous loads
let isLoading = false;
let isLoadingTransactions = false;

document.addEventListener('DOMContentLoaded', function() {
    // Authentication is handled server-side, so we can directly load data
    loadDashboardData();

    // Refresh button
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            if (!isLoading) {
                loadDashboardData();
            }
        });
    }
});

async function loadDashboardData() {
    // Prevent multiple simultaneous loads
    if (isLoading) {
        console.log('Dashboard already loading, skipping...');
        return;
    }

    try {
        isLoading = true;
        showLoading(true);
        console.log('Loading dashboard data...');

        // Load real dashboard data from API
        console.log('Making API requests to /dashboard and /dashboard/trends');
        const dashboardResponse = await apiRequest('/dashboard');
        const trendsResponse = await apiRequest('/dashboard/trends');

        console.log('Dashboard response status:', dashboardResponse.status);
        console.log('Trends response status:', trendsResponse.status);

        if (dashboardResponse.ok && trendsResponse.ok) {
            const dashboardResult = await dashboardResponse.json();
            const trendsResult = await trendsResponse.json();

            // Extract data from the API response format
            const dashboardData = dashboardResult.data;
            const trendsData = trendsResult.data;

            console.log('Successfully loaded dashboard data:', dashboardData);
            console.log('Successfully loaded trends data:', trendsData);

            renderSummaryCards(dashboardData);
            renderBudgetChart(dashboardData);
            renderBudgetStatus(dashboardData);
            renderCategoryBreakdown(dashboardData);
            renderTrendsChart(trendsData);
            loadRecentTransactions();
        } else {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data. Dashboard status:', dashboardResponse.status, 'Trends status:', trendsResponse.status);

            // Log response details for debugging
            if (!dashboardResponse.ok) {
                const dashboardError = await dashboardResponse.text();
                console.error('Dashboard API error:', dashboardError);
            }
            if (!trendsResponse.ok) {
                const trendsError = await trendsResponse.text();
                console.error('Trends API error:', trendsError);
            }

            loadMockDashboardData();
        }

    } catch (error) {
        console.error('Dashboard error:', error);
        // Fallback to mock data on error
        console.warn('Error loading real data, using mock data');
        loadMockDashboardData();
    } finally {
        isLoading = false;
        showLoading(false);
        console.log('Dashboard loading completed');
    }
}

// Fallback function with mock data
function loadMockDashboardData() {
    const mockDashboardData = {
        totalIncome: 5000,
        totalSpent: 3500,
        totalRemaining: 1500,
        overallBudgetStatus: 'under',
        budgetAllocation: {
            needs: 1250,
            wants: 1250,
            investments: 2250,
            donations: 250
        },
        summary: {
            needs: {
                allocated: 1250,
                spent: 800,
                remaining: 450,
                percentUsed: 64.0,
                isOverBudget: false
            },
            wants: {
                allocated: 1250,
                spent: 600,
                remaining: 650,
                percentUsed: 48.0,
                isOverBudget: false
            },
            investments: {
                allocated: 2250,
                spent: 1800,
                remaining: 450,
                percentUsed: 80.0,
                isOverBudget: false
            },
            donations: {
                allocated: 250,
                spent: 300,
                remaining: -50,
                percentUsed: 120.0,
                isOverBudget: true
            }
        }
    };

    const mockTrendsData = [
        { year: 2024, month: 1, income: 4800, needs: 750, wants: 500, investments: 1600, donations: 200 },
        { year: 2024, month: 2, income: 5200, needs: 820, wants: 650, investments: 1700, donations: 250 },
        { year: 2024, month: 3, income: 4900, needs: 780, wants: 580, investments: 1650, donations: 280 },
        { year: 2024, month: 4, income: 5100, needs: 800, wants: 620, investments: 1750, donations: 300 },
        { year: 2024, month: 5, income: 5300, needs: 850, wants: 680, investments: 1800, donations: 320 },
        { year: 2024, month: 6, income: 5000, needs: 800, wants: 600, investments: 1800, donations: 300 }
    ];

    renderSummaryCards(mockDashboardData);
    renderBudgetChart(mockDashboardData);
    renderBudgetStatus(mockDashboardData);
    renderCategoryBreakdown(mockDashboardData);
    renderTrendsChart(mockTrendsData);
    loadRecentTransactionsMock();
}

function renderSummaryCards(data) {
    const container = document.getElementById('summaryCards');

    const cards = [
        {
            title: 'Total Income',
            value: formatCurrency(data.totalIncome),
            icon: 'fas fa-money-bill-wave',
            color: 'success',
            gradient: 'bg-gradient-success',
            description: 'Monthly earnings',
            trend: '+12%',
            trendIcon: 'fas fa-arrow-up'
        },
        {
            title: 'Total Spent',
            value: formatCurrency(data.totalSpent),
            icon: 'fas fa-shopping-cart',
            color: 'danger',
            gradient: 'bg-gradient-danger',
            description: 'Total expenses',
            trend: '-5%',
            trendIcon: 'fas fa-arrow-down'
        },
        {
            title: 'Remaining Budget',
            value: formatCurrency(data.totalRemaining),
            icon: 'fas fa-piggy-bank',
            color: data.totalRemaining >= 0 ? 'info' : 'warning',
            gradient: data.totalRemaining >= 0 ? 'bg-gradient-info' : 'bg-gradient-warning',
            description: 'Available funds',
            trend: data.totalRemaining >= 0 ? '+8%' : '-15%',
            trendIcon: data.totalRemaining >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
        },
        {
            title: 'Budget Status',
            value: data.overallBudgetStatus.charAt(0).toUpperCase() + data.overallBudgetStatus.slice(1),
            icon: 'fas fa-chart-pie',
            color: data.overallBudgetStatus === 'over' ? 'danger' : 'success',
            gradient: data.overallBudgetStatus === 'over' ? 'bg-gradient-danger' : 'bg-gradient-success',
            description: 'Overall performance',
            trend: data.overallBudgetStatus === 'over' ? 'Over Budget' : 'On Track',
            trendIcon: data.overallBudgetStatus === 'over' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle'
        }
    ];

    container.innerHTML = cards.map((card, index) => `
        <div class="col-md-6 col-xl-3 mb-4">
            <div class="card ${card.gradient} text-white hover-lift fade-in" style="animation-delay: ${index * 0.1}s">
                <div class="card-body position-relative">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1 opacity-90">${card.title}</h6>
                            <p class="small mb-0 opacity-75">${card.description}</p>
                        </div>
                        <div class="card-icon">
                            <i class="${card.icon} fa-2x opacity-75"></i>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-end">
                        <div>
                            <h3 class="mb-1 fw-bold">${card.value}</h3>
                            <div class="d-flex align-items-center">
                                <i class="${card.trendIcon} me-1 small"></i>
                                <small class="opacity-90">${card.trend}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative elements -->
                    <div class="position-absolute top-0 end-0 p-3">
                        <div class="bg-white bg-opacity-10 rounded-circle p-2">
                            <i class="${card.icon}"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderBudgetChart(data) {
    const ctx = document.getElementById('budgetChart').getContext('2d');
    
    if (budgetChart) {
        budgetChart.destroy();
    }
    
    budgetChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Needs', 'Wants', 'Investments', 'Donations'],
            datasets: [{
                data: [
                    data.summary.needs.allocated,
                    data.summary.wants.allocated,
                    data.summary.investments.allocated,
                    data.summary.donations.allocated
                ],
                backgroundColor: [
                    '#dc3545', // Red for needs
                    '#ffc107', // Yellow for wants
                    '#28a745', // Green for investments
                    '#17a2b8'  // Blue for donations
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.parsed);
                            const percentage = ((context.parsed / data.totalIncome) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

function renderBudgetStatus(data) {
    const container = document.getElementById('budgetStatus');

    const statusColor = data.overallBudgetStatus === 'over' ? 'danger' :
                       data.overallBudgetStatus === 'under' ? 'success' : 'warning';

    const spentPercentage = (data.totalSpent / data.totalIncome) * 100;
    const remainingPercentage = 100 - spentPercentage;

    container.innerHTML = `
        <div class="text-center mb-4">
            <div class="badge bg-${statusColor} fs-6 mb-3 px-3 py-2">
                <i class="fas fa-${statusColor === 'success' ? 'check-circle' : statusColor === 'danger' ? 'exclamation-triangle' : 'clock'} me-2"></i>
                ${data.overallBudgetStatus.toUpperCase()} BUDGET
            </div>
        </div>

        <div class="budget-summary">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                        <i class="fas fa-money-bill-wave text-success"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Total Income</h6>
                        <small class="text-muted">Monthly earnings</small>
                    </div>
                </div>
                <div class="text-end">
                    <h6 class="mb-0 text-success">${formatCurrency(data.totalIncome)}</h6>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                        <i class="fas fa-shopping-cart text-danger"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Total Spent</h6>
                        <small class="text-muted">${spentPercentage.toFixed(1)}% of income</small>
                    </div>
                </div>
                <div class="text-end">
                    <h6 class="mb-0 text-danger">${formatCurrency(data.totalSpent)}</h6>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center">
                    <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3">
                        <i class="fas fa-piggy-bank text-info"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Remaining</h6>
                        <small class="text-muted">${remainingPercentage.toFixed(1)}% available</small>
                    </div>
                </div>
                <div class="text-end">
                    <h6 class="mb-0 text-${data.totalRemaining >= 0 ? 'info' : 'warning'}">${formatCurrency(data.totalRemaining)}</h6>
                </div>
            </div>

            <div class="progress mb-3" style="height: 10px;">
                <div class="progress-bar bg-${statusColor}"
                     style="width: ${Math.min(spentPercentage, 100)}%"
                     data-bs-toggle="tooltip"
                     title="${spentPercentage.toFixed(1)}% of budget used">
                </div>
            </div>

            <div class="text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    ${data.overallBudgetStatus === 'over' ? 'You have exceeded your budget' :
                      data.overallBudgetStatus === 'under' ? 'You are within your budget' :
                      'You are at your budget limit'}
                </small>
            </div>
        </div>
    `;

    // Initialize tooltips
    const tooltips = container.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
}

function renderCategoryBreakdown(data) {
    const container = document.getElementById('categoryBreakdown');

    const categories = [
        { name: 'needs', title: 'Needs', icon: 'fas fa-shopping-basket', color: 'danger', description: 'Essential expenses' },
        { name: 'wants', title: 'Wants', icon: 'fas fa-gift', color: 'warning', description: 'Discretionary spending' },
        { name: 'investments', title: 'Investments', icon: 'fas fa-chart-line', color: 'success', description: 'Future planning' },
        { name: 'donations', title: 'Donations', icon: 'fas fa-heart', color: 'info', description: 'Charitable giving' }
    ];

    container.innerHTML = categories.map((category, index) => {
        const categoryData = data.summary[category.name];
        const progressPercentage = Math.min((categoryData.spent / categoryData.allocated) * 100, 100);
        const progressColor = categoryData.isOverBudget ? 'danger' : 'success';
        const remainingPercentage = 100 - categoryData.percentUsed;

        return `
            <div class="col-md-6 col-xl-3 mb-4">
                <div class="card category-${category.name} hover-lift fade-in" style="animation-delay: ${index * 0.1}s">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="category-icon ${category.name}">
                                <i class="${category.icon}"></i>
                            </div>
                            ${categoryData.isOverBudget ?
                                '<span class="badge bg-danger pulse"><i class="fas fa-exclamation-triangle me-1"></i>Over</span>' :
                                '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Good</span>'
                            }
                        </div>

                        <div class="mb-3">
                            <h6 class="card-title mb-1">${category.title}</h6>
                            <small class="text-muted">${category.description}</small>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small text-muted">Budget Progress</span>
                                <span class="small fw-bold text-${progressColor}">${formatPercentage(categoryData.percentUsed)}</span>
                            </div>
                            <div class="progress progress-lg mb-2">
                                <div class="progress-bar bg-${progressColor}"
                                     style="width: ${progressPercentage}%"
                                     data-bs-toggle="tooltip"
                                     title="${formatCurrency(categoryData.spent)} of ${formatCurrency(categoryData.allocated)}">
                                </div>
                            </div>
                        </div>

                        <div class="row g-2 text-center">
                            <div class="col-4">
                                <div class="bg-light rounded p-2">
                                    <div class="small text-muted mb-1">Allocated</div>
                                    <div class="fw-bold small">${formatCurrencyWithUnits(categoryData.allocated)}</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-light rounded p-2">
                                    <div class="small text-muted mb-1">Spent</div>
                                    <div class="fw-bold small text-${category.color}">${formatCurrencyWithUnits(categoryData.spent)}</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-light rounded p-2">
                                    <div class="small text-muted mb-1">Remaining</div>
                                    <div class="fw-bold small text-${categoryData.remaining >= 0 ? 'success' : 'danger'}">${formatCurrencyWithUnits(categoryData.remaining)}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 d-flex gap-2">
                            <a href="/${category.name}" class="btn btn-sm btn-outline-${category.color} flex-fill">
                                <i class="fas fa-list me-1"></i>View
                            </a>
                            <a href="/${category.name}/new" class="btn btn-sm btn-${category.color} flex-fill">
                                <i class="fas fa-plus me-1"></i>Add
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // Initialize tooltips
    const tooltips = container.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    
    if (trendsChart) {
        trendsChart.destroy();
    }
    
    const labels = data.map(item => {
        const date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    
    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Income',
                    data: data.map(item => item.income),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Needs',
                    data: data.map(item => item.needs),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Wants',
                    data: data.map(item => item.wants),
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Investments',
                    data: data.map(item => item.investments),
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

async function loadRecentTransactions() {
    // Prevent multiple simultaneous loads
    if (isLoadingTransactions) {
        console.log('Recent transactions already loading, skipping...');
        return;
    }

    try {
        isLoadingTransactions = true;

        // Load recent transactions from all categories
        const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];
        const promises = categories.map(category =>
            apiRequest(`/${category}?limit=5&page=1`)
        );

        const responses = await Promise.all(promises);
        const allTransactions = [];

        for (let i = 0; i < responses.length; i++) {
            if (responses[i].ok) {
                const data = await responses[i].json();
                if (data.data && Array.isArray(data.data)) {
                    data.data.forEach(transaction => {
                        allTransactions.push({
                            ...transaction,
                            category: categories[i]
                        });
                    });
                }
            }
        }

        // Sort by date (newest first) and take top 10
        allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));
        const recentTransactions = allTransactions.slice(0, 10);

        renderRecentTransactions(recentTransactions);
    } catch (error) {
        console.error('Error loading recent transactions:', error);
        // Fallback to mock data
        loadRecentTransactionsMock();
    } finally {
        isLoadingTransactions = false;
    }
}

function renderRecentTransactions(transactions) {
    const tbody = document.querySelector('#recentTransactions tbody');

    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                        <h6>No recent transactions</h6>
                        <p class="mb-0">Start by adding your first transaction</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const categoryConfig = {
        incomes: { color: 'primary', icon: 'fas fa-money-bill-wave' },
        needs: { color: 'danger', icon: 'fas fa-shopping-basket' },
        wants: { color: 'warning', icon: 'fas fa-gift' },
        investments: { color: 'success', icon: 'fas fa-chart-line' },
        donations: { color: 'info', icon: 'fas fa-heart' }
    };

    tbody.innerHTML = transactions.map((transaction, index) => {
        const config = categoryConfig[transaction.category] || { color: 'secondary', icon: 'fas fa-tag' };
        const isIncome = transaction.category === 'incomes';

        return `
            <tr class="fade-in" style="animation-delay: ${index * 0.05}s">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="bg-${config.color} bg-opacity-10 rounded-circle p-2 me-2">
                            <i class="${config.icon} text-${config.color} small"></i>
                        </div>
                        <div>
                            <div class="fw-medium">${formatDate(transaction.date)}</div>
                            <small class="text-muted">${new Date(transaction.date).toLocaleDateString('en-US', { weekday: 'short' })}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${config.color} bg-opacity-10 text-${config.color} border border-${config.color} border-opacity-25">
                        <i class="${config.icon} me-1"></i>
                        ${transaction.category.charAt(0).toUpperCase() + transaction.category.slice(1)}
                    </span>
                </td>
                <td>
                    <div class="fw-medium">${transaction.description}</div>
                    ${transaction.notes ? `<small class="text-muted">${transaction.notes}</small>` : ''}
                </td>
                <td class="text-end">
                    <div class="fw-bold text-${isIncome ? 'success' : 'danger'}">
                        ${isIncome ? '+' : '-'}${formatCurrency(transaction.amount)}
                    </div>
                </td>
                <td class="text-center">
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="/${transaction.category}/${transaction._id}/edit"
                           class="btn btn-outline-primary btn-sm"
                           data-bs-toggle="tooltip"
                           title="Edit transaction">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-outline-danger btn-sm"
                                onclick="deleteTransaction('${transaction._id}', '${transaction.category}')"
                                data-bs-toggle="tooltip"
                                title="Delete transaction">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    // Initialize tooltips
    const tooltips = tbody.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
}

// Function to handle transaction deletion
function deleteTransaction(id, category) {
    if (confirm('Are you sure you want to delete this transaction?')) {
        // Add deletion logic here
        console.log(`Deleting transaction ${id} from ${category}`);
        // You can implement the actual deletion API call here
    }
}

function loadRecentTransactionsMock() {
    const mockTransactions = [
        { date: '2024-01-15', category: 'incomes', description: 'Salary Payment', amount: 5000 },
        { date: '2024-01-14', category: 'needs', description: 'Grocery Shopping', amount: 120 },
        { date: '2024-01-13', category: 'wants', description: 'Movie Tickets', amount: 25 },
        { date: '2024-01-12', category: 'investments', description: 'Stock Purchase', amount: 500 },
        { date: '2024-01-11', category: 'donations', description: 'Charity Donation', amount: 50 },
        { date: '2024-01-10', category: 'needs', description: 'Electricity Bill', amount: 80 },
        { date: '2024-01-09', category: 'wants', description: 'Coffee Shop', amount: 15 },
        { date: '2024-01-08', category: 'investments', description: 'Mutual Fund', amount: 300 }
    ];

    renderRecentTransactions(mockTransactions);
}

function showLoading(show) {
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
        if (show) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        } else {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
        }
    }
}
