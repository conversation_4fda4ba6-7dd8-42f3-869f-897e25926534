<nav class="navbar navbar-expand-lg navbar-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-chart-line me-2"></i>
            <span class="fw-bold">Dhanfolio</span>
        </a>

        <!-- Mobile toggle button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Main Navigation -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard" id="nav-dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <!-- Quick Add Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus-circle me-2"></i>
                        <span>Quick Add</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <h6 class="dropdown-header">
                                <i class="fas fa-plus me-2"></i>Add New Transaction
                            </h6>
                        </li>
                        <li><a class="dropdown-item" href="/incomes/new">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                            <span>Income</span>
                            <small class="text-muted ms-auto">Ctrl+I</small>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/needs/new">
                            <i class="fas fa-shopping-basket me-2 text-danger"></i>
                            <span>Need</span>
                            <small class="text-muted ms-auto">Ctrl+N</small>
                        </a></li>
                        <li><a class="dropdown-item" href="/wants/new">
                            <i class="fas fa-gift me-2 text-warning"></i>
                            <span>Want</span>
                            <small class="text-muted ms-auto">Ctrl+W</small>
                        </a></li>
                        <li><a class="dropdown-item" href="/investments/new">
                            <i class="fas fa-chart-line me-2 text-success"></i>
                            <span>Investment</span>
                            <small class="text-muted ms-auto">Ctrl+V</small>
                        </a></li>
                        <li><a class="dropdown-item" href="/donations/new">
                            <i class="fas fa-heart me-2 text-info"></i>
                            <span>Donation</span>
                            <small class="text-muted ms-auto">Ctrl+D</small>
                        </a></li>
                    </ul>
                </li>

                <!-- Transactions Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-list-alt me-2"></i>
                        <span>Transactions</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <h6 class="dropdown-header">
                                <i class="fas fa-eye me-2"></i>View Transactions
                            </h6>
                        </li>
                        <li><a class="dropdown-item" href="/incomes">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                            <span>Incomes</span>
                            <span class="badge bg-primary bg-opacity-10 text-primary ms-auto" id="income-count">0</span>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/needs">
                            <i class="fas fa-shopping-basket me-2 text-danger"></i>
                            <span>Needs</span>
                            <span class="badge bg-danger bg-opacity-10 text-danger ms-auto" id="needs-count">0</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/wants">
                            <i class="fas fa-gift me-2 text-warning"></i>
                            <span>Wants</span>
                            <span class="badge bg-warning bg-opacity-10 text-warning ms-auto" id="wants-count">0</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/investments">
                            <i class="fas fa-chart-line me-2 text-success"></i>
                            <span>Investments</span>
                            <span class="badge bg-success bg-opacity-10 text-success ms-auto" id="investments-count">0</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/donations">
                            <i class="fas fa-heart me-2 text-info"></i>
                            <span>Donations</span>
                            <span class="badge bg-info bg-opacity-10 text-info ms-auto" id="donations-count">0</span>
                        </a></li>
                    </ul>
                </li>

                <!-- Analytics -->
                <li class="nav-item">
                    <a class="nav-link" href="/analytics" id="nav-analytics">
                        <i class="fas fa-chart-bar me-2"></i>
                        <span>Analytics</span>
                    </a>
                </li>
            </ul>

            <!-- Right side navigation -->
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown me-2">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count" style="font-size: 0.6rem;">
                            3
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <li>
                            <h6 class="dropdown-header d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-bell me-2"></i>Notifications</span>
                                <button class="btn btn-sm btn-outline-primary">Mark all read</button>
                            </h6>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <h6 class="mb-1">Budget Alert</h6>
                                    <p class="mb-1 small">You've exceeded your "Wants" budget by ₹500</p>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                            </div>
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-info"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <h6 class="mb-1">Monthly Report</h6>
                                    <p class="mb-1 small">Your monthly financial report is ready</p>
                                    <small class="text-muted">1 day ago</small>
                                </div>
                            </div>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="/notifications">
                            <small>View all notifications</small>
                        </a></li>
                    </ul>
                </li>

                <!-- User Profile -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="bg-white bg-opacity-20 rounded-circle p-1 me-2">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="d-none d-md-inline"><%= user.fullName %></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <div class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                        <i class="fas fa-user text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><%= user.fullName %></h6>
                                        <small class="text-muted"><%= user.email %></small>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/profile">
                            <i class="fas fa-user-edit me-2"></i>Edit Profile
                        </a></li>
                        <li><a class="dropdown-item" href="/settings">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><a class="dropdown-item" href="/help">
                            <i class="fas fa-question-circle me-2"></i>Help & Support
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>Sign Out
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="bg-light border-bottom" id="breadcrumb-nav" style="display: none;">
    <div class="container-fluid">
        <ol class="breadcrumb mb-0 py-2">
            <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page" id="current-page">Current Page</li>
        </ol>
    </div>
</nav>

<script>
// Add active state to navigation items
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case 'i':
                    e.preventDefault();
                    window.location.href = '/incomes/new';
                    break;
                case 'n':
                    e.preventDefault();
                    window.location.href = '/needs/new';
                    break;
                case 'w':
                    e.preventDefault();
                    window.location.href = '/wants/new';
                    break;
                case 'v':
                    e.preventDefault();
                    window.location.href = '/investments/new';
                    break;
                case 'd':
                    e.preventDefault();
                    window.location.href = '/donations/new';
                    break;
            }
        }
    });
});
</script>
