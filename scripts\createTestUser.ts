import mongoose from 'mongoose';
import { config } from '../src/config/env';
import { AuthService } from '../src/services/authService';
import { User } from '../src/models/User';

const createTestUser = async () => {
  try {
    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Test user already exists');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      process.exit(0);
    }

    // Create test user
    const user = await AuthService.registerUser(
      '<EMAIL>',
      'password123',
      'Test User'
    );

    console.log('Test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('User ID:', user._id);

  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
};

createTestUser();
