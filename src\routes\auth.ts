import { Router } from 'express';
import { AuthController } from '../controllers';
import { validate } from '../middlewares/validation';
import { authenticate } from '../middlewares/auth';
import { registerSchema, loginSchema, refreshTokenSchema } from '../utils/validation';

const router = Router();

router.post('/register', validate(registerSchema), AuthController.register);
router.post('/login', validate(loginSchema), AuthController.login);
router.post('/refresh', validate(refreshTokenSchema), AuthController.refresh);
router.post('/logout', authenticate, AuthController.logout);
router.post('/logout-all', authenticate, AuthController.logoutAll);

export default router;
