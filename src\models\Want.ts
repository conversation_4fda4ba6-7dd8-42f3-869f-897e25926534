import mongoose, { Document, Schema } from 'mongoose';

export interface IWant extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number;
  description: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

const wantSchema = new Schema<IWant>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Index for better query performance
wantSchema.index({ userId: 1, date: -1 });

export const Want = mongoose.model<IWant>('Want', wantSchema);
