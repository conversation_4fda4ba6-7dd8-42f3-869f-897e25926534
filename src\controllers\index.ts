import { TransactionController } from './transactionController';
import { Income, Need, Want, Investment, Donation } from '../models';

// Create controller instances for each category
export const incomeController = new TransactionController(Income, 'Income');
export const needController = new TransactionController(Need, 'Need');
export const wantController = new TransactionController(Want, 'Want');
export const investmentController = new TransactionController(Investment, 'Investment');
export const donationController = new TransactionController(Donation, 'Donation');

export { AuthController } from './authController';
export { UserController } from './userController';
