// Transaction form functionality
document.addEventListener('DOMContentLoaded', function() {
    // For demo purposes, skip authentication check
    // if (!requireAuth()) return;

    const form = document.getElementById('transactionForm');
    const submitBtn = document.getElementById('submitBtn');
    const deleteBtn = document.getElementById('deleteBtn');

    // Form submission
    if (form) {
        form.addEventListener('submit', handleSubmit);
    }

    // Delete button (if editing)
    if (deleteBtn) {
        deleteBtn.addEventListener('click', showDeleteModal);
        const confirmDeleteBtn = document.getElementById('confirmDelete');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', confirmDelete);
        }
    }

    // Auto-resize textarea
    const textarea = document.getElementById('description');
    if (textarea) {
        textarea.addEventListener('input', autoResize);
        autoResize.call(textarea);
    }
});

async function handleSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);

    // Convert amount to number
    data.amount = parseFloat(data.amount);

    // Convert date to ISO string
    data.date = new Date(data.date).toISOString();

    try {
        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.innerHTML;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

        // Make actual API call
        const categoryName = window.formData?.categoryName || 'incomes';
        const isEdit = window.formData?.isEdit;
        const transactionId = window.formData?.transactionId;

        let response;
        if (isEdit && transactionId) {
            // Update existing transaction
            response = await apiRequest(`/${categoryName}/${transactionId}`, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        } else {
            // Create new transaction
            response = await apiRequest(`/${categoryName}`, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }

        if (response.ok) {
            const result = await response.json();
            const action = isEdit ? 'updated' : 'created';
            const categoryTitle = window.formData?.categoryTitle || 'Transaction';
            showAlert('success', `${categoryTitle.slice(0, -1)} ${action} successfully!`);

            // Redirect to transactions list after a short delay
            setTimeout(() => {
                window.location.href = `/${categoryName}`;
            }, 1500);
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to save transaction');

            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }

    } catch (error) {
        console.error('Form submission error:', error);
        showAlert('error', 'Error saving transaction');

        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = false;
        const categoryTitle = window.formData?.categoryTitle || 'Transaction';
        const action = window.formData?.isEdit ? 'Update' : 'Save';
        submitBtn.innerHTML = `<i class="fas fa-save me-2"></i>${action} ${categoryTitle.slice(0, -1)}`;
    }
}

function showDeleteModal() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

async function confirmDelete() {
    if (!window.formData.isEdit || !window.formData.transactionId) return;
    
    try {
        const response = await apiRequest(`/${window.formData.categoryName}/${window.formData.transactionId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showAlert('success', `${window.formData.categoryTitle.slice(0, -1)} deleted successfully`);
            
            // Redirect to transactions list after a short delay
            setTimeout(() => {
                window.location.href = `/${window.formData.categoryName}`;
            }, 1500);
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to delete transaction');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showAlert('error', 'Error deleting transaction');
    }
    
    // Hide modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
    modal.hide();
}

function autoResize() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const descriptionInput = document.getElementById('description');
    const dateInput = document.getElementById('date');
    
    // Amount validation
    amountInput.addEventListener('input', function() {
        const value = parseFloat(this.value);
        if (isNaN(value) || value <= 0) {
            this.setCustomValidity('Amount must be a positive number');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Description validation
    descriptionInput.addEventListener('input', function() {
        if (this.value.trim().length < 3) {
            this.setCustomValidity('Description must be at least 3 characters long');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Date validation
    dateInput.addEventListener('input', function() {
        const selectedDate = new Date(this.value);
        const today = new Date();
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        
        if (selectedDate > today) {
            this.setCustomValidity('Date cannot be in the future');
        } else if (selectedDate < oneYearAgo) {
            this.setCustomValidity('Date cannot be more than one year ago');
        } else {
            this.setCustomValidity('');
        }
    });
});
