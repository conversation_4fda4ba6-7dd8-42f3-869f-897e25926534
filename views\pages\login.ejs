<div class="auth-container">
    <div class="auth-card slide-up">
        <div class="auth-header">
            <div class="auth-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <h2>Welcome Back</h2>
            <p>Sign in to your Dhanfolio account</p>
        </div>

        <div class="auth-form">
            <form id="loginForm">
                <div class="mb-4">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email"
                               class="form-control"
                               id="email"
                               name="email"
                               placeholder="Enter your email"
                               required>
                    </div>
                    <div class="invalid-feedback" id="emailError"></div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password"
                               class="form-control"
                               id="password"
                               name="password"
                               placeholder="Enter your password"
                               required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="passwordError"></div>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me for 30 days
                        </label>
                    </div>
                </div>

                <div class="d-grid mb-4">
                    <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span class="btn-text">Sign In</span>
                    </button>
                </div>

                <div class="text-center">
                    <a href="#" class="text-decoration-none text-primary-600">
                        <small>Forgot your password?</small>
                    </a>
                </div>
            </form>
        </div>

        <div class="auth-footer">
            <p class="mb-0">
                Don't have an account?
                <a href="/register">Create one now</a>
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const emailInput = document.getElementById('email');

    // Add floating label effect
    function handleFloatingLabel(input) {
        const label = input.previousElementSibling;
        if (input.value || input === document.activeElement) {
            label.classList.add('active');
        } else {
            label.classList.remove('active');
        }
    }

    // Toggle password visibility with smooth animation
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        const icon = this.querySelector('i');
        icon.style.transform = 'scale(0.8)';

        setTimeout(() => {
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
            icon.style.transform = 'scale(1)';
        }, 100);
    });

    // Enhanced form validation
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        const errorDiv = document.getElementById(field.id + 'Error');
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        const errorDiv = document.getElementById(field.id + 'Error');
        if (errorDiv) {
            errorDiv.textContent = '';
        }
    }

    // Real-time validation
    emailInput.addEventListener('blur', function() {
        if (this.value && !validateEmail(this.value)) {
            showFieldError(this, 'Please enter a valid email address');
        } else if (this.value) {
            clearFieldError(this);
        }
    });

    passwordInput.addEventListener('blur', function() {
        if (this.value && this.value.length < 6) {
            showFieldError(this, 'Password must be at least 6 characters');
        } else if (this.value) {
            clearFieldError(this);
        }
    });

    // Enhanced form submission with better UX
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear previous errors
        emailInput.classList.remove('is-invalid', 'is-valid');
        passwordInput.classList.remove('is-invalid', 'is-valid');

        // Validate form
        let isValid = true;

        if (!emailInput.value) {
            showFieldError(emailInput, 'Email is required');
            isValid = false;
        } else if (!validateEmail(emailInput.value)) {
            showFieldError(emailInput, 'Please enter a valid email address');
            isValid = false;
        }

        if (!passwordInput.value) {
            showFieldError(passwordInput, 'Password is required');
            isValid = false;
        } else if (passwordInput.value.length < 6) {
            showFieldError(passwordInput, 'Password must be at least 6 characters');
            isValid = false;
        }

        if (!isValid) return;

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        try {
            // Enhanced loading state
            loginBtn.disabled = true;
            loginBtn.classList.add('loading');
            const btnText = loginBtn.querySelector('.btn-text');
            const originalText = btnText.textContent;
            btnText.textContent = 'Signing In...';

            // Add spinner
            const spinner = document.createElement('div');
            spinner.className = 'spinner spinner-sm me-2';
            loginBtn.insertBefore(spinner, btnText);

            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                // Store user data and tokens for client-side access
                localStorage.setItem('user', JSON.stringify(result.user));
                localStorage.setItem('tokens', JSON.stringify(result.tokens));

                // Success animation
                loginBtn.classList.remove('btn-primary');
                loginBtn.classList.add('btn-success');
                btnText.textContent = 'Success!';

                // Show success message and redirect
                showAlert('success', 'Welcome back! Redirecting to your dashboard...');

                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            } else {
                // Handle specific error cases
                if (result.error && result.error.includes('email')) {
                    showFieldError(emailInput, result.error);
                } else if (result.error && result.error.includes('password')) {
                    showFieldError(passwordInput, result.error);
                } else {
                    showAlert('danger', result.error || 'Login failed. Please try again.');
                }
            }
        } catch (error) {
            showAlert('danger', 'Network error. Please check your connection and try again.');
        } finally {
            // Reset button state
            if (!loginBtn.classList.contains('btn-success')) {
                loginBtn.disabled = false;
                loginBtn.classList.remove('loading');
                const spinner = loginBtn.querySelector('.spinner');
                if (spinner) spinner.remove();
                const btnText = loginBtn.querySelector('.btn-text');
                btnText.textContent = 'Sign In';
            }
        }
    });

    // Add subtle animations on focus
    const inputs = [emailInput, passwordInput];
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
