/* Modern Design System for Dhanfolio */

:root {
    /* Primary Brand Colors */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;

    /* Semantic Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --danger-50: #fef2f2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;

    --info-50: #f0f9ff;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-700: #0e7490;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);

    /* Text Colors */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);

    /* Border Colors */
    --border-light: var(--gray-200);
    --border-medium: var(--gray-300);
    --border-dark: var(--gray-400);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Global styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--primary-50) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-primary);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-400));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.75rem;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    transform: translateY(-1px);
    color: var(--primary-100) !important;
}

.navbar-brand i {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #fff, var(--primary-100));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
    margin: 0 var(--space-1);
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
}

.navbar-toggler {
    border: none;
    padding: var(--space-1);
    border-radius: var(--radius-md);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.25);
}

.dropdown-menu {
    background: white;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
    min-width: 200px;
}

.dropdown-item {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dropdown-item:hover {
    background: var(--primary-50);
    color: var(--primary-700);
    transform: translateX(2px);
}

.dropdown-divider {
    margin: var(--space-2) 0;
    border-color: var(--border-light);
}

/* Cards */
.card {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    background: var(--bg-primary);
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

.card-header {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--gray-50) 100%);
    border-bottom: 1px solid var(--border-light);
    font-weight: 600;
    padding: var(--space-6);
    color: var(--text-primary);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--border-light);
    padding: var(--space-4) var(--space-6);
}

/* Enhanced Card Variants */
.card-gradient {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: none;
}

.card-gradient .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.card-success {
    border-left: 4px solid var(--success-500);
    background: linear-gradient(135deg, var(--success-50) 0%, white 100%);
}

.card-danger {
    border-left: 4px solid var(--danger-500);
    background: linear-gradient(135deg, var(--danger-50) 0%, white 100%);
}

.card-warning {
    border-left: 4px solid var(--warning-500);
    background: linear-gradient(135deg, var(--warning-50) 0%, white 100%);
}

.card-info {
    border-left: 4px solid var(--info-500);
    background: linear-gradient(135deg, var(--info-50) 0%, white 100%);
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.dashboard-card .card-body {
    padding: var(--space-6);
    position: relative;
    z-index: 1;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Buttons */
.btn {
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: var(--transition-fast);
    padding: var(--space-3) var(--space-6);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-800);
    border-color: var(--border-dark);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-500);
}

.btn-outline-primary:hover {
    background: var(--primary-500);
    color: white;
    border-color: var(--primary-500);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-600);
    border: 2px solid var(--gray-300);
}

.btn-outline-secondary:hover {
    background: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-400);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: 0.75rem;
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: 1rem;
    border-radius: var(--radius-xl);
}

.btn-group .btn {
    transform: none;
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
}

.btn-group .btn:hover {
    transform: none;
    z-index: 1;
}

/* Floating Action Button */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: var(--space-8);
    right: var(--space-8);
    z-index: 1000;
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: none;
    font-size: 1.25rem;
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
    color: white;
}

/* Forms */
.form-control {
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    transition: var(--transition-fast);
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    outline: none;
    background: white;
}

.form-control:hover {
    border-color: var(--border-medium);
}

.form-control::placeholder {
    color: var(--text-tertiary);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
}

.input-group {
    position: relative;
}

.input-group-text {
    background: var(--gray-100);
    border: 2px solid var(--border-light);
    border-right: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary-500);
    background: var(--primary-50);
    color: var(--primary-700);
}

.input-group:focus-within .form-control {
    border-color: var(--primary-500);
}

.form-select {
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-primary);
    transition: var(--transition-fast);
}

.form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-check-input {
    border-radius: var(--radius-sm);
    border: 2px solid var(--border-medium);
    transition: var(--transition-fast);
}

.form-check-input:checked {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
}

.form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-check-label {
    font-weight: 500;
    color: var(--text-primary);
}

/* Form Validation */
.is-valid {
    border-color: var(--success-500) !important;
}

.is-invalid {
    border-color: var(--danger-500) !important;
}

.valid-feedback {
    color: var(--success-600);
    font-weight: 500;
    font-size: 0.75rem;
    margin-top: var(--space-1);
}

.invalid-feedback {
    color: var(--danger-600);
    font-weight: 500;
    font-size: 0.75rem;
    margin-top: var(--space-1);
}

/* Enhanced Form Groups */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    padding: var(--space-4) var(--space-4) var(--space-2) var(--space-4);
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: var(--space-4);
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition-fast);
    color: var(--text-tertiary);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-600);
}

/* Tables */
.table {
    border-radius: var(--radius-xl);
    overflow: hidden;
    border: 1px solid var(--border-light);
    background: var(--bg-primary);
}

.table thead th {
    border-top: none;
    font-weight: 600;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    color: var(--text-primary);
    padding: var(--space-4) var(--space-6);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    border-bottom: 2px solid var(--border-light);
}

.table tbody td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.table-hover tbody tr {
    transition: var(--transition-fast);
}

.table-hover tbody tr:hover {
    background: var(--primary-50);
    transform: scale(1.01);
}

.table-responsive {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

/* Table Actions */
.table-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.table-actions .btn {
    padding: var(--space-1) var(--space-2);
    font-size: 0.75rem;
}

/* Striped Tables */
.table-striped tbody tr:nth-of-type(odd) {
    background: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(even) {
    background: white;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--radius-xl);
    font-weight: 500;
    padding: var(--space-4) var(--space-6);
    margin-bottom: var(--space-4);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: linear-gradient(135deg, var(--success-50) 0%, white 100%);
    color: var(--success-700);
    border-left: 4px solid var(--success-500);
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-50) 0%, white 100%);
    color: var(--danger-700);
    border-left: 4px solid var(--danger-500);
}

.alert-warning {
    background: linear-gradient(135deg, var(--warning-50) 0%, white 100%);
    color: var(--warning-700);
    border-left: 4px solid var(--warning-500);
}

.alert-info {
    background: linear-gradient(135deg, var(--info-50) 0%, white 100%);
    color: var(--info-700);
    border-left: 4px solid var(--info-500);
}

.alert-dismissible .btn-close {
    padding: var(--space-3) var(--space-4);
    background: none;
    border: none;
    opacity: 0.6;
    transition: var(--transition-fast);
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Toast Notifications */
.toast {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
}

.toast-header {
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.toast-body {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* Progress bars */
.progress {
    border-radius: var(--radius-xl);
    background: var(--gray-200);
    height: 8px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: var(--radius-xl);
    background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-400) 100%);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

.progress-sm {
    height: 4px;
}

.progress-lg {
    height: 12px;
}

/* Badges */
.badge {
    font-weight: 600;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: white;
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    color: white;
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    color: white;
}

.badge-info {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
    color: white;
}

.badge-light {
    background: var(--gray-100);
    color: var(--gray-700);
}

.badge-dark {
    background: var(--gray-800);
    color: white;
}

/* Charts */
canvas {
    max-height: 400px;
    border-radius: var(--radius-lg);
}

.chart-container {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-sm);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-200);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--space-2);
}

.skeleton-text:last-child {
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    width: 100%;
}

/* Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm {
    width: 16px;
    height: 16px;
}

.spinner-lg {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

/* Category colors */
.category-needs {
    border-left: 4px solid var(--danger-500);
    background: linear-gradient(135deg, var(--danger-50) 0%, white 100%);
}

.category-wants {
    border-left: 4px solid var(--warning-500);
    background: linear-gradient(135deg, var(--warning-50) 0%, white 100%);
}

.category-investments {
    border-left: 4px solid var(--success-500);
    background: linear-gradient(135deg, var(--success-50) 0%, white 100%);
}

.category-donations {
    border-left: 4px solid var(--info-500);
    background: linear-gradient(135deg, var(--info-50) 0%, white 100%);
}

.category-incomes {
    border-left: 4px solid var(--primary-500);
    background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
}

/* Category Icons */
.category-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: var(--space-3);
}

.category-icon.needs {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    color: white;
}

.category-icon.wants {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    color: white;
}

.category-icon.investments {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: white;
}

.category-icon.donations {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
    color: white;
}

.category-icon.incomes {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-6);
}

.modal-title {
    font-weight: 600;
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--border-light);
    padding: var(--space-4) var(--space-6);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

.btn-close {
    background: none;
    border: none;
    opacity: 0.6;
    transition: var(--transition-fast);
    padding: var(--space-2);
    border-radius: var(--radius-md);
}

.btn-close:hover {
    opacity: 1;
    background: var(--gray-100);
}

/* Pagination */
.pagination {
    gap: var(--space-1);
}

.page-link {
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    padding: var(--space-2) var(--space-3);
    transition: var(--transition-fast);
    text-decoration: none;
    font-weight: 500;
}

.page-link:hover {
    background: var(--primary-50);
    border-color: var(--primary-200);
    color: var(--primary-700);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-color: var(--primary-500);
    color: white;
}

.page-item.disabled .page-link {
    opacity: 0.5;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.4s ease-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s;
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition-fast);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    :root {
        --space-4: 0.75rem;
        --space-6: 1rem;
        --space-8: 1.5rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .card-body {
        padding: var(--space-4);
    }

    .btn {
        padding: var(--space-2) var(--space-4);
        font-size: 0.875rem;
    }

    .btn-lg {
        padding: var(--space-3) var(--space-6);
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-body {
        padding: var(--space-4);
    }

    .modal-header {
        padding: var(--space-4);
    }

    .btn-fab {
        width: 48px;
        height: 48px;
        bottom: var(--space-4);
        right: var(--space-4);
    }

    .dropdown-menu {
        min-width: 180px;
    }
}

/* Authentication pages */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 50%, var(--primary-900) 100%);
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    position: relative;
    z-index: 1;
    max-width: 400px;
    width: 100%;
    margin: var(--space-4);
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-400) 50%, var(--primary-600) 100%);
}

.auth-header {
    text-align: center;
    padding: var(--space-8) var(--space-6) var(--space-4);
}

.auth-header .auth-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
}

.auth-header h2 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.auth-header p {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.auth-form {
    padding: 0 var(--space-6) var(--space-8);
}

.auth-divider {
    margin: var(--space-6) 0;
    text-align: center;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-light);
}

.auth-divider span {
    background: white;
    padding: 0 var(--space-4);
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

.auth-footer {
    text-align: center;
    padding: var(--space-4) var(--space-6);
    background: var(--gray-50);
    border-top: 1px solid var(--border-light);
}

.auth-footer a {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.auth-footer a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Footer */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%) !important;
    color: var(--gray-300) !important;
    border-top: 1px solid var(--gray-700);
}

footer p {
    margin: 0;
    font-size: 0.875rem;
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-radius-sm {
    border-radius: var(--radius-sm) !important;
}

.border-radius-md {
    border-radius: var(--radius-md) !important;
}

.border-radius-lg {
    border-radius: var(--radius-lg) !important;
}

.border-radius-xl {
    border-radius: var(--radius-xl) !important;
}

.border-radius-2xl {
    border-radius: var(--radius-2xl) !important;
}

.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
    box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.shadow-xl {
    box-shadow: var(--shadow-xl) !important;
}

/* Spacing utilities */
.gap-1 { gap: var(--space-1) !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }
.gap-4 { gap: var(--space-4) !important; }
.gap-5 { gap: var(--space-5) !important; }
.gap-6 { gap: var(--space-6) !important; }

/* Background utilities */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%) !important;
}

/* Text utilities */
.text-primary-600 { color: var(--primary-600) !important; }
.text-success-600 { color: var(--success-600) !important; }
.text-danger-600 { color: var(--danger-600) !important; }
.text-warning-600 { color: var(--warning-600) !important; }
.text-info-600 { color: var(--info-600) !important; }

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

::-webkit-scrollbar-corner {
    background: var(--gray-100);
}

/* Focus states */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid var(--border-light) !important;
    }

    .btn {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
    }
}
