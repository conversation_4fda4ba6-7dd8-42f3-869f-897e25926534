import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  try {
    console.log('🧪 Testing API endpoints...\n');

    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await fetch(`${BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);

    // Test 2: Login
    console.log('\n2. Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed:', await loginResponse.text());
      return;
    }

    const loginData = await loginResponse.json() as any;
    console.log('✅ Login successful');
    console.log('🔍 Login response:', loginData);

    const accessToken = loginData.tokens?.accessToken;
    if (!accessToken) {
      console.log('❌ No access token received');
      return;
    }

    // Test 3: Dashboard data
    console.log('\n3. Testing dashboard data...');
    const dashboardResponse = await fetch(`${BASE_URL}/api/dashboard`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!dashboardResponse.ok) {
      console.log('❌ Dashboard failed:', await dashboardResponse.text());
      return;
    }

    const dashboardData = await dashboardResponse.json() as any;
    console.log('✅ Dashboard data received');
    console.log('📊 Dashboard summary:', {
      totalIncome: dashboardData.data.totalIncome,
      totalSpent: dashboardData.data.totalSpent,
      totalRemaining: dashboardData.data.totalRemaining
    });

    // Test 4: Monthly trends
    console.log('\n4. Testing monthly trends...');
    const trendsResponse = await fetch(`${BASE_URL}/api/dashboard/trends`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!trendsResponse.ok) {
      console.log('❌ Trends failed:', await trendsResponse.text());
      return;
    }

    const trendsData = await trendsResponse.json() as any;
    console.log('✅ Trends data received');
    console.log('📈 Trends count:', trendsData.data.length);

    // Test 5: Income transactions
    console.log('\n5. Testing income transactions...');
    const incomeResponse = await fetch(`${BASE_URL}/api/incomes?limit=5`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!incomeResponse.ok) {
      console.log('❌ Income failed:', await incomeResponse.text());
      return;
    }

    const incomeData = await incomeResponse.json() as any;
    console.log('✅ Income data received');
    console.log('💰 Income count:', incomeData.data.length);

    console.log('\n🎉 All tests passed! API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAPI();
