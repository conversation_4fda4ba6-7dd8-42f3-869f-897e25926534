<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <!-- Header Section -->
        <div class="card mb-4 border-0 bg-gradient-primary text-white">
            <div class="card-body text-center py-4">
                <div class="category-icon <%= categoryName %> bg-white bg-opacity-20 mx-auto mb-3">
                    <i class="<%= categoryIcon %>"></i>
                </div>
                <h3 class="mb-2">
                    <%= isEdit ? 'Edit' : 'Add New' %> <%= categoryTitle.slice(0, -1) %>
                </h3>
                <p class="mb-0 opacity-90">
                    <%= isEdit ? 'Update your transaction details' : 'Track your financial activity' %>
                </p>
            </div>
        </div>

        <!-- Form Section -->
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2 text-primary"></i>Transaction Details
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="fas fa-arrow-left me-1"></i>Back
                        </button>
                        <% if (isEdit) { %>
                        <button type="button" class="btn btn-outline-danger" id="deleteBtn">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                        <% } %>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form id="transactionForm" class="needs-validation" novalidate>
                    <!-- Amount Field -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="amount" class="form-label">
                                <i class="fas fa-rupee-sign me-1 text-success"></i>
                                Amount <span class="text-danger">*</span>
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-success bg-opacity-10 text-success border-success border-opacity-25">
                                    <i class="fas fa-rupee-sign"></i>
                                </span>
                                <input type="number"
                                       class="form-control border-success border-opacity-25"
                                       id="amount"
                                       name="amount"
                                       step="0.01"
                                       min="0"
                                       required
                                       placeholder="Enter amount"
                                       value="<%= transaction ? transaction.amount : '' %>">
                                <div class="invalid-feedback">
                                    Please enter a valid amount.
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Enter the transaction amount in Indian Rupees
                            </div>
                        </div>

                        <!-- Quick Amount Buttons -->
                        <div class="col-md-6">
                            <label class="form-label">Quick Amount</label>
                            <div class="d-grid gap-2">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="100">₹100</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="500">₹500</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="1000">₹1,000</button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="2000">₹2,000</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="5000">₹5,000</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" data-amount="10000">₹10,000</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  required
                                  placeholder="Enter a description for this transaction..."><%= transaction ? transaction.description : '' %></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date" class="form-label">Date *</label>
                        <input type="date" 
                               class="form-control" 
                               id="date" 
                               name="date" 
                               required
                               value="<%= transaction ? transaction.date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0] %>">
                    </div>
                    
                    <% if (categoryName !== 'incomes') { %>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Budget Allocation:</strong> 
                        <% if (categoryName === 'needs') { %>
                            25% of your income should go to needs (essentials like food, housing, utilities).
                        <% } else if (categoryName === 'wants') { %>
                            25% of your income should go to wants (entertainment, dining out, hobbies).
                        <% } else if (categoryName === 'investments') { %>
                            45% of your income should go to investments (savings, stocks, retirement).
                        <% } else if (categoryName === 'donations') { %>
                            5% of your income should go to donations (charity, giving back).
                        <% } %>
                    </div>
                    <% } %>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            <%= isEdit ? 'Update' : 'Save' %> <%= categoryTitle.slice(0, -1) %>
                        </button>
                        <a href="/<%= categoryName %>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <% if (isEdit) { %>
                        <button type="button" class="btn btn-danger ms-auto" id="deleteBtn">
                            <i class="fas fa-trash me-2"></i>Delete
                        </button>
                        <% } %>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<% if (isEdit) { %>
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this <%= categoryTitle.slice(0, -1).toLowerCase() %>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
<% } %>

<script>
// Pass data to JavaScript
window.formData = {
    categoryName: '<%= categoryName %>',
    categoryTitle: '<%= categoryTitle %>',
    isEdit: <%= isEdit %>,
    transactionId: '<%= transaction ? transaction._id : '' %>'
};
</script>
<script src="/js/transaction-form.js"></script>
