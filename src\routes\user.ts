import { Router } from 'express';
import { UserController } from '../controllers';
import { authenticate } from '../middlewares/auth';
import { validate } from '../middlewares/validation';
import { z } from 'zod';

const router = Router();

// All user routes require authentication
router.use(authenticate);

// Validation schemas
const updateProfileSchema = z.object({
  fullName: z.string().min(1).max(100).optional(),
  email: z.string().email().optional()
}).refine(data => data.fullName || data.email, {
  message: "At least one field (fullName or email) is required"
});

// User profile routes
router.get('/profile', UserController.getProfile);
router.put('/profile', validate(updateProfileSchema), UserController.updateProfile);

// Device management routes
router.get('/devices', UserController.getDevices);
router.delete('/devices/:deviceId', UserController.removeDevice);

export default router;
