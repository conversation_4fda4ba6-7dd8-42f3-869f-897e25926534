# Dhanfolio - Personal Finance Tracker

A comprehensive personal finance tracking application built with TypeScript, Express, MongoDB, and EJS. Track your income and expenses across four budget categories with intelligent allocation recommendations.

## 🎯 Features

- **JWT Authentication** with device management (max 3 devices per user)
- **Budget Allocation System** (25% Needs, 25% Wants, 45% Investments, 5% Donations)
- **Transaction Management** for all categories
- **Interactive Dashboard** with charts and analytics
- **Monthly Trends** visualization
- **Responsive Web Interface** built with Bootstrap 5
- **RESTful API** for all operations
- **Security Features** (rate limiting, CORS, helmet, sanitization)

## 🛠️ Tech Stack

### Backend
- **Node.js** with **Express.js**
- **TypeScript** (strict mode)
- **MongoDB** with **Mongoose** ODM
- **JWT** for authentication
- **bcryptjs** for password hashing
- **Zod** for input validation
- **EJS** for server-side rendering

### Frontend
- **EJS** templates
- **Bootstrap 5** for styling
- **Chart.js** for data visualization
- **Font Awesome** for icons
- **Vanilla JavaScript** for interactivity

### Security & Middleware
- **Helmet** for security headers
- **CORS** for cross-origin requests
- **Express Rate Limit** for API protection
- **Express Mongo Sanitize** for NoSQL injection prevention

## 📁 Project Structure

```
dhanfolio-backend/
├── src/
│   ├── config/          # Database and environment configuration
│   ├── models/          # Mongoose models
│   ├── controllers/     # Route handlers
│   ├── services/        # Business logic
│   ├── routes/          # Express routes
│   ├── middlewares/     # Custom middleware
│   ├── utils/           # Helper functions
│   ├── app.ts           # Express app configuration
│   └── server.ts        # Application entry point
├── views/
│   ├── layouts/         # EJS layout templates
│   ├── pages/           # Page templates
│   └── partials/        # Reusable components
├── public/
│   ├── css/             # Stylesheets
│   ├── js/              # Client-side JavaScript
│   └── images/          # Static images
├── .env.example         # Environment variables template
├── tsconfig.json        # TypeScript configuration
└── package.json         # Dependencies and scripts
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher)
- **MongoDB** (v5 or higher)
- **npm** or **yarn**

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dhanfolio-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   PORT=3000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/dhanfolio
   JWT_SECRET=your-super-secret-jwt-key
   REFRESH_TOKEN_SECRET=your-super-secret-refresh-token-key
   JWT_EXPIRES_IN=15m
   REFRESH_TOKEN_EXPIRES_IN=7d
   BCRYPT_ROUNDS=12
   MAX_DEVICES_PER_USER=3
   ```

4. **Start MongoDB**
   ```bash
   # Using MongoDB service
   sudo systemctl start mongod
   
   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

5. **Run the application**
   ```bash
   # Development mode
   npm run dev
   
   # Production build
   npm run build
   npm start
   ```

6. **Access the application**
   - Web Interface: http://localhost:3000
   - API Health Check: http://localhost:3000/health

## 📊 Budget Allocation Logic

Dhanfolio follows a proven budget allocation strategy:

- **Needs (25%)**: Essential expenses (housing, food, utilities, transportation)
- **Wants (25%)**: Discretionary spending (entertainment, dining out, hobbies)
- **Investments (45%)**: Savings, stocks, retirement funds, emergency fund
- **Donations (5%)**: Charitable giving, community support

## 🔐 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout from current device
- `POST /api/auth/logout-all` - Logout from all devices

### Dashboard
- `GET /api/dashboard` - Get dashboard data with budget analysis
- `GET /api/dashboard/trends` - Get monthly trends data

### Transactions (CRUD for each category)
- `GET /api/{category}` - List transactions with pagination
- `POST /api/{category}` - Create new transaction
- `GET /api/{category}/:id` - Get specific transaction
- `PUT /api/{category}/:id` - Update transaction
- `DELETE /api/{category}/:id` - Delete transaction

**Categories**: `incomes`, `needs`, `wants`, `investments`, `donations`

## 🎨 Frontend Pages

- **Authentication**: Login and registration pages
- **Dashboard**: Overview with budget charts and recent transactions
- **Transaction Lists**: Paginated lists for each category
- **Transaction Forms**: Add/edit forms with validation
- **Error Pages**: 404 and 500 error handling

## 🔒 Security Features

- **JWT Authentication** with refresh tokens
- **Device Management** (max 3 concurrent sessions)
- **Rate Limiting** (100 requests per 15 minutes, 5 auth requests per 15 minutes)
- **Input Validation** using Zod schemas
- **Password Hashing** with bcrypt (12 rounds)
- **CORS Protection** with configurable origins
- **Security Headers** via Helmet
- **NoSQL Injection Prevention** via sanitization

## 📱 Device Management

- Users can be logged in on maximum 3 devices simultaneously
- When logging in on a 4th device, the oldest session is automatically removed
- Each device session includes user agent, IP address, and last seen timestamp
- Users can logout from current device or all devices

## 🧪 Development

### Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm start           # Start production server
npm run lint        # Run ESLint
npm run format      # Format code with Prettier
```

### Code Quality

- **TypeScript** with strict mode enabled
- **ESLint** for code linting
- **Prettier** for code formatting
- **Consistent** error handling and validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.

## 🙏 Acknowledgments

- Bootstrap team for the excellent CSS framework
- Chart.js for beautiful data visualizations
- MongoDB team for the robust database solution
- Express.js community for the fantastic web framework
