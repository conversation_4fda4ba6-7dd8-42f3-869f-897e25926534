<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
            <div class="dashboard-header">
                <h1 class="h2 mb-2 text-gradient">
                    <i class="fas fa-tachometer-alt me-3"></i>Financial Dashboard
                </h1>
                <p class="text-secondary mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Welcome back! Here's your financial overview for today.
                </p>
            </div>

            <div class="dashboard-actions d-flex gap-2">
                <button type="button" class="btn btn-outline-primary" id="refreshData">
                    <i class="fas fa-sync-alt me-2"></i>
                    <span class="d-none d-sm-inline">Refresh</span>
                </button>

                <div class="dropdown">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-plus me-2"></i>
                        <span class="d-none d-sm-inline">Quick Add</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <h6 class="dropdown-header">
                                <i class="fas fa-plus-circle me-2"></i>Add Transaction
                            </h6>
                        </li>
                        <li><a class="dropdown-item" href="/incomes/new">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i>Income
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/needs/new">
                            <i class="fas fa-shopping-basket me-2 text-danger"></i>Need
                        </a></li>
                        <li><a class="dropdown-item" href="/wants/new">
                            <i class="fas fa-gift me-2 text-warning"></i>Want
                        </a></li>
                        <li><a class="dropdown-item" href="/investments/new">
                            <i class="fas fa-chart-line me-2 text-success"></i>Investment
                        </a></li>
                        <li><a class="dropdown-item" href="/donations/new">
                            <i class="fas fa-heart me-2 text-info"></i>Donation
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-5" id="summaryCards">
    <!-- Enhanced cards will be populated by JavaScript -->
    <div class="col-12 text-center">
        <div class="skeleton skeleton-card" style="height: 120px;"></div>
    </div>
</div>

<!-- Budget Overview -->
<div class="row mb-5">
    <div class="col-lg-8 mb-4">
        <div class="card hover-lift">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-chart-pie me-2"></i>Budget Allocation
                        </h5>
                        <small class="opacity-75">Your spending distribution across categories</small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>Export Chart</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container">
                    <canvas id="budgetChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card hover-lift h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2 text-primary"></i>Budget Status
                </h5>
            </div>
            <div class="card-body d-flex flex-column" id="budgetStatus">
                <!-- Enhanced status will be populated by JavaScript -->
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text"></div>
            </div>
        </div>
    </div>
</div>

<!-- Category Breakdown -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-layer-group me-2 text-primary"></i>Category Breakdown
                        </h5>
                        <small class="text-muted">Detailed view of your spending by category</small>
                    </div>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="categoryView" id="cardView" checked>
                        <label class="btn btn-outline-primary" for="cardView">
                            <i class="fas fa-th-large"></i>
                        </label>
                        <input type="radio" class="btn-check" name="categoryView" id="listView">
                        <label class="btn btn-outline-primary" for="listView">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row" id="categoryBreakdown">
                    <!-- Enhanced category cards will be populated by JavaScript -->
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="skeleton skeleton-card"></div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="skeleton skeleton-card"></div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="skeleton skeleton-card"></div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="skeleton skeleton-card"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-chart-line me-2 text-success"></i>Monthly Trends
                        </h5>
                        <small class="text-muted">Track your financial patterns over time</small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-alt me-2"></i>Last 6 Months
                            <i class="fas fa-chevron-down ms-1"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Last 3 Months</a></li>
                            <li><a class="dropdown-item" href="#">Last 6 Months</a></li>
                            <li><a class="dropdown-item" href="#">Last Year</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="trendsChart" width="400" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-lift">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">
                            <i class="fas fa-history me-2 text-info"></i>Recent Transactions
                        </h5>
                        <small class="text-muted">Your latest financial activities</small>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" id="filterTransactions">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                        <a href="/transactions" class="btn btn-sm btn-primary">
                            <i class="fas fa-external-link-alt me-1"></i>View All
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped" id="recentTransactions">
                        <thead>
                            <tr>
                                <th class="border-0">
                                    <i class="fas fa-calendar-alt me-1"></i>Date
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-tag me-1"></i>Category
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-file-alt me-1"></i>Description
                                </th>
                                <th class="border-0 text-end">
                                    <i class="fas fa-rupee-sign me-1"></i>Amount
                                </th>
                                <th class="border-0 text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Enhanced transactions will be populated by JavaScript -->
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner me-2"></div>
                                        <span class="text-muted">Loading transactions...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats Footer -->
<div class="row">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3 mb-md-0">
                        <div class="d-flex flex-column">
                            <i class="fas fa-calendar-week fa-2x mb-2 opacity-75"></i>
                            <h6 class="mb-1">This Week</h6>
                            <span class="h5 mb-0" id="weeklySpending">₹0</span>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <div class="d-flex flex-column">
                            <i class="fas fa-calendar-alt fa-2x mb-2 opacity-75"></i>
                            <h6 class="mb-1">This Month</h6>
                            <span class="h5 mb-0" id="monthlySpending">₹0</span>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3 mb-md-0">
                        <div class="d-flex flex-column">
                            <i class="fas fa-chart-line fa-2x mb-2 opacity-75"></i>
                            <h6 class="mb-1">Avg. Daily</h6>
                            <span class="h5 mb-0" id="dailyAverage">₹0</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column">
                            <i class="fas fa-target fa-2x mb-2 opacity-75"></i>
                            <h6 class="mb-1">Budget Goal</h6>
                            <span class="h5 mb-0" id="budgetGoal">₹0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<button class="btn btn-fab" data-bs-toggle="dropdown" aria-expanded="false">
    <i class="fas fa-plus"></i>
</button>
<ul class="dropdown-menu dropdown-menu-end">
    <li><a class="dropdown-item" href="/incomes/new">
        <i class="fas fa-money-bill-wave me-2 text-primary"></i>Add Income
    </a></li>
    <li><hr class="dropdown-divider"></li>
    <li><a class="dropdown-item" href="/needs/new">
        <i class="fas fa-shopping-basket me-2 text-danger"></i>Add Need
    </a></li>
    <li><a class="dropdown-item" href="/wants/new">
        <i class="fas fa-gift me-2 text-warning"></i>Add Want
    </a></li>
    <li><a class="dropdown-item" href="/investments/new">
        <i class="fas fa-chart-line me-2 text-success"></i>Add Investment
    </a></li>
    <li><a class="dropdown-item" href="/donations/new">
        <i class="fas fa-heart me-2 text-info"></i>Add Donation
    </a></li>
</ul>

<script src="/js/dashboard.js"></script>
