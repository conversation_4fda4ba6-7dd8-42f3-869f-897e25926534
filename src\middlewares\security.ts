import rateLimit from 'express-rate-limit';
import mongoSanitize from 'express-mongo-sanitize';
import helmet from 'helmet';
import cors from 'cors';
import { config } from '../config/env';

// Rate limiting
export const createRateLimit = (windowMs?: number, max?: number) => {
  return rateLimit({
    windowMs: windowMs || config.RATE_LIMIT_WINDOW_MS,
    max: max || config.RATE_LIMIT_MAX_REQUESTS,
    message: {
      error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// Auth-specific rate limiting (more lenient for development)
export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  config.NODE_ENV === 'development' ? 50 : 5 // 50 requests in dev, 5 in production
);

// General API rate limiting (more lenient for development)
export const apiRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  config.NODE_ENV === 'development' ? 1000 : 100 // 1000 requests in dev, 100 in production
);

// CORS configuration
export const corsOptions = {
  origin: function (origin: string | undefined, callback: Function) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    // In development, allow all origins
    if (config.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    // In production, you should specify allowed origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      // Add your production frontend URLs here
    ];
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
};

// Security middleware setup
export const setupSecurity = () => {
  return [
    // Helmet for security headers
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net",
            "https://cdnjs.cloudflare.com"
          ],
          scriptSrc: [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net",
            "https://cdnjs.cloudflare.com"
          ],
          imgSrc: ["'self'", "data:", "https:"],
          fontSrc: [
            "'self'",
            "https://cdnjs.cloudflare.com",
            "data:"
          ],
          connectSrc: ["'self'"],
        },
      },
    }),

    // CORS
    cors(corsOptions),

    // Data sanitization against NoSQL query injection
    mongoSanitize(),
  ];
};
