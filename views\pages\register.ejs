<div class="auth-container">
    <div class="auth-card slide-up">
        <div class="auth-header">
            <div class="auth-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <h2>Create Your Account</h2>
            <p>Join <PERSON> and take control of your finances</p>
        </div>

        <div class="auth-form">
            <form id="registerForm">
                <div class="mb-4">
                    <label for="fullName" class="form-label">Full Name</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text"
                               class="form-control"
                               id="fullName"
                               name="fullName"
                               placeholder="Enter your full name"
                               required>
                    </div>
                    <div class="invalid-feedback" id="fullNameError"></div>
                </div>

                <div class="mb-4">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email"
                               class="form-control"
                               id="email"
                               name="email"
                               placeholder="Enter your email"
                               required>
                    </div>
                    <div class="invalid-feedback" id="emailError"></div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password"
                               class="form-control"
                               id="password"
                               name="password"
                               placeholder="Create a strong password"
                               required
                               minlength="6">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="passwordError"></div>
                    <div class="form-text">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Password must be at least 6 characters long
                        </small>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password"
                               class="form-control"
                               id="confirmPassword"
                               name="confirmPassword"
                               placeholder="Confirm your password"
                               required>
                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="confirmPasswordError"></div>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the
                            <a href="#" class="text-primary-600 text-decoration-none">Terms of Service</a>
                            and
                            <a href="#" class="text-primary-600 text-decoration-none">Privacy Policy</a>
                        </label>
                    </div>
                    <div class="invalid-feedback" id="termsError"></div>
                </div>

                <div class="d-grid mb-4">
                    <button type="submit" class="btn btn-primary btn-lg" id="registerBtn">
                        <i class="fas fa-user-plus me-2"></i>
                        <span class="btn-text">Create Account</span>
                    </button>
                </div>
            </form>
        </div>

        <div class="auth-footer">
            <p class="mb-0">
                Already have an account?
                <a href="/login">Sign in here</a>
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const registerBtn = document.getElementById('registerBtn');
    const fullNameInput = document.getElementById('fullName');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const termsInput = document.getElementById('terms');
    const togglePassword = document.getElementById('togglePassword');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');

    // Enhanced password visibility toggle
    function togglePasswordVisibility(input, button) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);

        const icon = button.querySelector('i');
        icon.style.transform = 'scale(0.8)';

        setTimeout(() => {
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
            icon.style.transform = 'scale(1)';
        }, 100);
    }

    togglePassword.addEventListener('click', function() {
        togglePasswordVisibility(passwordInput, this);
    });

    toggleConfirmPassword.addEventListener('click', function() {
        togglePasswordVisibility(confirmPasswordInput, this);
    });

    // Enhanced validation functions
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    function validatePassword(password) {
        return password.length >= 6;
    }

    function validateFullName(name) {
        return name.trim().length >= 2;
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        const errorDiv = document.getElementById(field.id + 'Error');
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        const errorDiv = document.getElementById(field.id + 'Error');
        if (errorDiv) {
            errorDiv.textContent = '';
        }
    }

    // Real-time validation
    fullNameInput.addEventListener('blur', function() {
        if (this.value && !validateFullName(this.value)) {
            showFieldError(this, 'Please enter your full name (at least 2 characters)');
        } else if (this.value) {
            clearFieldError(this);
        }
    });

    emailInput.addEventListener('blur', function() {
        if (this.value && !validateEmail(this.value)) {
            showFieldError(this, 'Please enter a valid email address');
        } else if (this.value) {
            clearFieldError(this);
        }
    });

    passwordInput.addEventListener('blur', function() {
        if (this.value && !validatePassword(this.value)) {
            showFieldError(this, 'Password must be at least 6 characters long');
        } else if (this.value) {
            clearFieldError(this);
        }

        // Re-validate confirm password if it has a value
        if (confirmPasswordInput.value) {
            validateConfirmPassword();
        }
    });

    function validateConfirmPassword() {
        if (confirmPasswordInput.value !== passwordInput.value) {
            showFieldError(confirmPasswordInput, 'Passwords do not match');
            return false;
        } else if (confirmPasswordInput.value) {
            clearFieldError(confirmPasswordInput);
            return true;
        }
        return false;
    }

    confirmPasswordInput.addEventListener('blur', validateConfirmPassword);
    confirmPasswordInput.addEventListener('input', function() {
        if (this.value && passwordInput.value) {
            validateConfirmPassword();
        }
    });

    // Enhanced form submission
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear all previous validation states
        [fullNameInput, emailInput, passwordInput, confirmPasswordInput, termsInput].forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });

        // Comprehensive validation
        let isValid = true;

        if (!fullNameInput.value.trim()) {
            showFieldError(fullNameInput, 'Full name is required');
            isValid = false;
        } else if (!validateFullName(fullNameInput.value)) {
            showFieldError(fullNameInput, 'Please enter your full name (at least 2 characters)');
            isValid = false;
        }

        if (!emailInput.value) {
            showFieldError(emailInput, 'Email is required');
            isValid = false;
        } else if (!validateEmail(emailInput.value)) {
            showFieldError(emailInput, 'Please enter a valid email address');
            isValid = false;
        }

        if (!passwordInput.value) {
            showFieldError(passwordInput, 'Password is required');
            isValid = false;
        } else if (!validatePassword(passwordInput.value)) {
            showFieldError(passwordInput, 'Password must be at least 6 characters long');
            isValid = false;
        }

        if (!confirmPasswordInput.value) {
            showFieldError(confirmPasswordInput, 'Please confirm your password');
            isValid = false;
        } else if (confirmPasswordInput.value !== passwordInput.value) {
            showFieldError(confirmPasswordInput, 'Passwords do not match');
            isValid = false;
        }

        if (!termsInput.checked) {
            showFieldError(termsInput, 'You must agree to the terms and conditions');
            isValid = false;
        }

        if (!isValid) {
            // Focus on first invalid field
            const firstInvalid = registerForm.querySelector('.is-invalid');
            if (firstInvalid) firstInvalid.focus();
            return;
        }

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Remove confirmPassword and terms from data
        delete data.confirmPassword;
        delete data.terms;

        try {
            // Enhanced loading state
            registerBtn.disabled = true;
            registerBtn.classList.add('loading');
            const btnText = registerBtn.querySelector('.btn-text');
            const originalText = btnText.textContent;
            btnText.textContent = 'Creating Account...';

            // Add spinner
            const spinner = document.createElement('div');
            spinner.className = 'spinner spinner-sm me-2';
            registerBtn.insertBefore(spinner, btnText);

            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                // Success animation
                registerBtn.classList.remove('btn-primary');
                registerBtn.classList.add('btn-success');
                btnText.textContent = 'Account Created!';

                showAlert('success', 'Welcome to Dhanfolio! Redirecting to sign in...');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else {
                // Handle specific error cases
                if (result.error && result.error.includes('email')) {
                    showFieldError(emailInput, result.error);
                } else {
                    showAlert('danger', result.error || 'Registration failed. Please try again.');
                }
            }
        } catch (error) {
            showAlert('danger', 'Network error. Please check your connection and try again.');
        } finally {
            // Reset button state
            if (!registerBtn.classList.contains('btn-success')) {
                registerBtn.disabled = false;
                registerBtn.classList.remove('loading');
                const spinner = registerBtn.querySelector('.spinner');
                if (spinner) spinner.remove();
                const btnText = registerBtn.querySelector('.btn-text');
                btnText.textContent = 'Create Account';
            }
        }
    });

    // Add subtle animations on focus
    const inputs = [fullNameInput, emailInput, passwordInput, confirmPasswordInput];
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
