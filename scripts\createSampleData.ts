import mongoose from 'mongoose';
import { config } from '../src/config/env';
import { User, Income, Need, Want, Investment, Donation } from '../src/models';

const createSampleData = async () => {
  try {
    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find test user
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      console.log('Test user not found. Please run createTestUser.ts first.');
      process.exit(1);
    }

    console.log('Found test user:', user.fullName);

    // Clear existing data for this user
    await Promise.all([
      Income.deleteMany({ userId: user._id }),
      Need.deleteMany({ userId: user._id }),
      Want.deleteMany({ userId: user._id }),
      Investment.deleteMany({ userId: user._id }),
      Donation.deleteMany({ userId: user._id })
    ]);

    // Create sample incomes
    const incomes = [
      { amount: 75000, description: 'Salary - Software Engineer', date: new Date('2024-01-01') },
      { amount: 15000, description: 'Freelance Project', date: new Date('2024-01-15') },
      { amount: 80000, description: 'Salary - Software Engineer', date: new Date('2024-02-01') },
      { amount: 5000, description: 'Bonus', date: new Date('2024-02-15') },
      { amount: 82000, description: 'Salary - Software Engineer', date: new Date('2024-03-01') }
    ];

    // Create sample needs (25% of income)
    const needs = [
      { amount: 25000, description: 'Rent', date: new Date('2024-01-01') },
      { amount: 8000, description: 'Groceries', date: new Date('2024-01-05') },
      { amount: 3000, description: 'Utilities', date: new Date('2024-01-10') },
      { amount: 2000, description: 'Transportation', date: new Date('2024-01-12') },
      { amount: 25000, description: 'Rent', date: new Date('2024-02-01') },
      { amount: 9000, description: 'Groceries', date: new Date('2024-02-05') },
      { amount: 3200, description: 'Utilities', date: new Date('2024-02-10') }
    ];

    // Create sample wants (25% of income)
    const wants = [
      { amount: 5000, description: 'Dining Out', date: new Date('2024-01-03') },
      { amount: 3000, description: 'Movies & Entertainment', date: new Date('2024-01-08') },
      { amount: 8000, description: 'New Clothes', date: new Date('2024-01-20') },
      { amount: 4000, description: 'Gaming', date: new Date('2024-02-03') },
      { amount: 6000, description: 'Weekend Trip', date: new Date('2024-02-18') },
      { amount: 2500, description: 'Books', date: new Date('2024-03-05') }
    ];

    // Create sample investments (45% of income)
    const investments = [
      { amount: 30000, description: 'SIP - Mutual Fund', date: new Date('2024-01-01') },
      { amount: 10000, description: 'Stock Purchase - TCS', date: new Date('2024-01-15') },
      { amount: 5000, description: 'PPF Contribution', date: new Date('2024-01-20') },
      { amount: 32000, description: 'SIP - Mutual Fund', date: new Date('2024-02-01') },
      { amount: 15000, description: 'Stock Purchase - Infosys', date: new Date('2024-02-10') },
      { amount: 8000, description: 'Gold ETF', date: new Date('2024-02-25') }
    ];

    // Create sample donations (5% of income)
    const donations = [
      { amount: 2000, description: 'Local NGO', date: new Date('2024-01-05') },
      { amount: 1500, description: 'Education Fund', date: new Date('2024-01-25') },
      { amount: 3000, description: 'Disaster Relief', date: new Date('2024-02-08') },
      { amount: 1000, description: 'Animal Shelter', date: new Date('2024-02-20') },
      { amount: 2500, description: 'Environmental Cause', date: new Date('2024-03-10') }
    ];

    // Insert all data
    await Promise.all([
      Income.insertMany(incomes.map(item => ({ ...item, userId: user._id }))),
      Need.insertMany(needs.map(item => ({ ...item, userId: user._id }))),
      Want.insertMany(wants.map(item => ({ ...item, userId: user._id }))),
      Investment.insertMany(investments.map(item => ({ ...item, userId: user._id }))),
      Donation.insertMany(donations.map(item => ({ ...item, userId: user._id })))
    ]);

    console.log('Sample data created successfully!');
    console.log(`Created ${incomes.length} income records`);
    console.log(`Created ${needs.length} need records`);
    console.log(`Created ${wants.length} want records`);
    console.log(`Created ${investments.length} investment records`);
    console.log(`Created ${donations.length} donation records`);

  } catch (error) {
    console.error('Error creating sample data:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
};

createSampleData();
