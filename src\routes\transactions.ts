import { Router } from 'express';
import { TransactionController } from '../controllers/transactionController';
import { validate, validateQuery } from '../middlewares/validation';
import { authenticate } from '../middlewares/auth';
import { transactionSchema, updateTransactionSchema, querySchema } from '../utils/validation';

export const createTransactionRoutes = (controller: TransactionController): Router => {
  const router = Router();

  // All routes require authentication
  router.use(authenticate);

  router.post('/', validate(transactionSchema), controller.create);
  router.get('/', validateQuery(querySchema), controller.getAll);
  router.get('/:id', controller.getById);
  router.put('/:id', validate(updateTransactionSchema), controller.update);
  router.delete('/:id', controller.delete);

  return router;
};
